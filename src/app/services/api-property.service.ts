import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';
import { Observable } from 'rxjs';
import { EnumCountry } from '../enumerations/county';
import { PropertyResearchStatusSaveInput, PropertyResearchStatus } from '../models/PropertyResearchStatus';
import { Property, ParcelDetails } from '../models/Property';
import { PropertyLocation } from '../models/PropertyLocation';
import { PropertyAdditionalAddress } from '../models/PropertyAdditionalAddress';
import { PropertyParcel } from '../models/PropertyParcel';
import { EnumApplication } from '../enumerations/application';
import { ISaleAutoEnrichRequest } from '../models/saleAutoEnrichRequest';

@Injectable()
export class PropertyService extends ApiBaseService {


    public GetAllQuadrants() {       
        const response = this.httpGet(this._serviceURL + 'lookup/quadrant', null);
        return response;
    }

    public GetAllEnergyStarRating() {
        const response = this.httpGet(this._serviceURL + 'lookup/EnergyStarRating');
        return response;
    }

    public GetAllPrefix() {
        const response = this.httpGet(this._serviceURL + 'lookup/streetPrefix', null);
        return response;       
    }

    public GetAllSuffix() {
        const response = this.httpGet(this._serviceURL + 'lookup/streetSuffix');
        return response;     
    }

    public GetAllState() {
        const response = this.httpGet(this._serviceURL + 'lookup/state');
        return response;       
    }

    public GetCitiesByState(stateId) {
        const response = this.httpGet(this._serviceURL + 'lookup/city/' + stateId);
        return response;
    }

    public GetAllCondoType() {
        const response = this.httpGet(this._serviceURL + 'lookup/condoType');
        return response;      
    }
    public GetAllComplexType() {
        const response = this.httpGet(this._serviceURL + 'lookup/complexType');
        return response;
    }

    public GetAllRoofTopSource() {
        const response = this.httpGet(this._serviceURL + 'lookup/roofTopSource');
        return response;      
    }



    /*
       public GetAllSpecificUse(){
           let response = this._http.get(this._serviceURL+'lookup/specificUse',{ headers : this._getHeaders});
           return response;
       }*/


    public GetAllSizeSource() {
        const response = this.httpGet(this._serviceURL + 'lookup/sizeSource');
        return response;       
    }
    public GetAllClass() {
        const response = this.httpGet(this._serviceURL + 'lookup/classType');
        return response;     
    }

    public GetAllGovInterest() {
        const response = this.httpGet(this._serviceURL + 'lookup/govInterest');
        return response;      
    }

    public GetAllConstructStatus() {
        const response = this.httpGet(this._serviceURL + 'lookup/constructStatus');
        return response;       
    }
    public GetAllConstructType() {
        const response = this.httpGet(this._serviceURL + 'lookup/constructType');
        return response;       
    }
    public GetAllPropertyType() {
        const response = this.httpGet(this._serviceURL + 'lookup/propertyType');
        return response;
    }

    public GetAllSpecificUse() {
        const response = this.httpGet(this._serviceURL + 'lookup/specificUse');
        return response;       
    }

    public GetAllSprinklerType() {
        const response = this.httpGet(this._serviceURL + 'lookup/sprinklerType');
        return response;       
    }
    public GetAllhvacType() {
        const response = this.httpGet(this._serviceURL + 'lookup/hvacType');
        return response;       
    }

    public GetAllLeedStatus() {
        const response = this.httpGet(this._serviceURL + 'lookup/leedStatus');
        return response;
    }

    public GetAllPropertyResearchStatus() {
        const response = this.httpGet(this._serviceURL + 'research/researchtype');
        return response;      
    }

    public GetAllBuildSpecStatus() {
        const response = this.httpGet(this._serviceURL + 'lookup/buildSpecStatus');
        return response;       
    }

    public GetAllRoofType() {
        const response = this.httpGet(this._serviceURL + 'lookup/roofType');
        return response;      
    }

    public GetAllCleanRoomClass() {
        const response = this.httpGet(this._serviceURL + 'lookup/clnRoomClass');
        return response;       
    }

    public GetAllEarthQuakeZone() {
        const response = this.httpGet(this._serviceURL + 'lookup/earthQuakeZone');
        return response;
    }

    public GetAllTenancy() {
        const response = this.httpGet(this._serviceURL + 'lookup/tenancy');
        return response;
    }

    public GetAllzoningClass() {
        const response = this.httpGet(this._serviceURL + 'lookup/zoningClass');
        return response;    

    }

    public GetAllzoningCode() {
        const response = this.httpGet(this._serviceURL + 'lookup/zoningCode');
        return response;
    }

    public GetAllListingStatus() {
        const response = this.httpGet(this._serviceURL + 'lookup/listingStatus');
        return response;
    }

    public GetListingType() {
        const response = this.httpGet(this._serviceURL + 'lookup/listingType');
        return response;
    }
    public GetRecordType() {
        const response = this.httpGet(this._serviceURL + 'lookup/recordType');
        return response;
    }

    public GetAllShareLevels() {
        const response = this.httpGet(this._serviceURL + 'lookup/shareLevel');
        return response;
    }

    public GetListingDetails(propertyId: number): Observable<any> {
        const response = this.httpGet(this._serviceURL + 'property/' + propertyId);
        return response;
    }
    public GetCompanyList() {
        const data = { company_id: '', search_val: '%' };
        const response = this.httpPost(this._serviceURL + 'lookup/company', JSON.stringify(data));
        return response;
    }
    public GetSuiteStatus() {
        const response = this.httpGet(this._serviceURL + 'lookup/suiteStatus');
        return response;
    }
    public GetSpaceType() {
        const response = this.httpGet(this._serviceURL + 'lookup/spaceType');
        return response;
    }
    public GetAmpType() {
        const response = this.httpGet(this._serviceURL + 'lookup/ampType');
        return response;
    }
    public GetVoltsType() {
        const response = this.httpGet(this._serviceURL + 'lookup/voltsType');
        return response;
    }
    public GetCompanyAgents(companyId: any, roleId: any): Observable<any> {
        const response = this.httpGet(this._serviceURL + 'lookup/company/agent/' + companyId + ' / ' + roleId);
        return response;
    }
    public GetCountryList(): Observable<any> {

        const response = this.httpGet(this._serviceURL + 'LookUp/country/');
        return response;      

    }

    public GetCountiesByState(stateId: number): Observable<any> {
        const response = this.httpGet(this._serviceURL + 'LookUp/CountyByStateId/' + stateId);
        return response;
    }
    public GetBuildingClass(): Observable<any> {
        const response = this.httpGet(this._serviceURL + 'LookUp/classType');
        return response;       
    }

    public GetAllNoteTypes() {
        const response = this.httpGet(this._serviceURL + '/LookUp/notetypes');
        return response;
    }


    // To get state by country.
    public GetStateListByCountryId(countryId: number): Observable<any> {

        const response = this.httpGet(this._serviceURL + 'LookUp/state/' + countryId);
        return response;
    }

    public GetSpecificUseByGenUseId(genUseId: number): Observable<any> {

        const response = this.httpGet(this._serviceURL + 'LookUp/getSpecificUse/' + genUseId);
        return response;
    }
    public getPropertyStrataDetails(property_Id: any, SortBy: any, SortDirection: any) {
        const response = this.httpGet(this._serviceURL + 'property/strataDetails/' + property_Id + '/' + SortBy + '/' + SortDirection);
        return response;
    }

    public findObjectByKey(array, key, value) {
        for (let i = 0; i < array.length; i++) {
            if (array[i][key] === value) {
                return array[i];
            }
        }
        return null;
    }




    public PropertySave(property: Property) {
        const response = this.httpPost(this._serviceURL + 'property', JSON.stringify(property));

        response.subscribe(result => console.log(result), error => console.log(error));
        return response;
    }
    public propertyLocationSave(plocation: PropertyLocation) {
        const response = this.httpPost(this._serviceURL + 'property/Location', JSON.stringify(plocation));
        return response;

    }

    public GetPropertyLocationByPropertyId(pid: number , SearchType : number) {
        const response = this.httpGet(this._serviceURL + 'property/locationDetails/' + pid + '/' + SearchType);
        return response;

    }

    encodeValue(value: string): string {
        return encodeURIComponent(value);
    }

    public GetPropertydetails(SearchText, isMaster) {
        SearchText = this.encodeValue(SearchText);
        const response = this.httpGet(this._serviceURL + 'lookup/Propertydetails/' + SearchText + '/' + (!!isMaster ? 1 : 0));
        return response;
    }

    public propertyDetailsSave(property: Property) {
        const response = this.httpPost(this._serviceURL + 'property/Details', JSON.stringify(property));
        return response;
    }

    // To get property details.
    public GetPropertyDetailsByPropertyId(propertyId: number): Observable<any> {
        const response = this.httpGet(this._serviceURL + 'property/' + propertyId,  { applicationID: EnumApplication.VST });
        return response;
    }


    public getMapBoundProperties(swLat: number, swLng: number, neLat: number, neLng: number, polygonText :any) {
        const bound = { SWLat: swLat, SWLng: swLng, NELat: neLat, NELng: neLng , PolygonText : polygonText};
        const response = this.httpPost(this._serviceURL + 'property/mapsearch/', JSON.stringify(bound));
        return response;
    }

    // public getIDMapBoundProperties(propertyId: number) {
    //     const idrequest = { PropertyID: propertyId };
    //     const response = this.httpPost(this._serviceURL + 'property/idmapsearch/', JSON.stringify(idrequest));
    //     return response;
    // }
    public getIDMapBoundProperties(searchText: number, searchType : number) {
        const idrequest = { SearchText: searchText, SearchType : searchType };
        const response = this.httpPost(this._serviceURL + 'property/idmapsearch/', JSON.stringify(idrequest));
        return response;
    }

    public convertUnit(countryId, from, to, value) {

        if (countryId === EnumCountry.Australia || countryId === EnumCountry.SouthAfrica) {
            if (from === 'SF' && to === 'SqM') {
                // tslint:disable-next-line:curly
                if (value)
                    value = value * 0.092903;

            } else if (from === 'SqM' && to === 'SF') {
                // tslint:disable-next-line:curly
                if (value)
                    value = value / 0.092903;
            } else if (from === 'ft' && to === 'M') {
                // tslint:disable-next-line:curly
                if (value)
                    value = value / 0.3048;
            } else if (from === 'M' && to === 'ft') {
                // tslint:disable-next-line:curly
                if (value)
                    value = value * 0.3048;
            }
            return value;
        }
    }
    public propertyResearchStatusSave(researchStatus: PropertyResearchStatus) {
        console.log("research status API call")
        const response = this.httpPost(this._serviceURL + 'research/ResearchStatusSave', JSON.stringify(researchStatus));
        return response;
    }
    public getPropertyResearchStatus(propertyId: number) {
        const response = this.httpGet(this._serviceURL + 'research/researchStatus/' + propertyId);
        return response;
    }

    public linkChildsToMaster(requestBody: any) {
        const response = this.httpPost(this._serviceURL + 'property/link-childs-to-master', JSON.stringify(requestBody));
        return response;
    }

    //Get property parcel details
    public GetPropertyParcelDetails(propertyId: number) {
        const response = this.httpGet(this._serviceURL + 'property/propertyParcelDetails/' + propertyId);
        return response;
    }

    public RelocateProperty(propertyId: number, latitude: number, Longitude: number, EntityID: number) {
        let data: any = {};
        data.PropertyId = propertyId;
        data.Latitude = latitude;
        data.Longitude = Longitude;
        data.EntityID = EntityID;
        const response = this.httpPost(this._serviceURL + '/property/relocate', data);
        return response;
    }

    public GetParcelShape(latitude: number, longitude: number) {
        const response = this.httpGet(`${this._serviceURL}/gis/parcelShape/${latitude}/${longitude}`);
        return response;
    }

    public getPropertyAdditionalAddress(propertyId: number) {
        const response = this.httpGet(this._serviceURL + 'property/AdditionalAddress/' + propertyId, { applicationID: EnumApplication.VST });
        return response;
    }
    public savePropertyAdditionalAddress(additionalAddress: PropertyAdditionalAddress) {
        const response = this.httpPost(this._serviceURL + 'property/AdditionalAddress', additionalAddress);
        return response;
    }

    public getPropertyAdditionalAddressByAddressId(addressId: number) {
        const response = this.httpGet(this._serviceURL + 'property/AdditionalAddressByAddressId/' + addressId);
        return response;
    }

    public PropertyAdditionalAddressDelete(additionalAddress: PropertyAdditionalAddress) {
        const response = this.httpPost(this._serviceURL + 'property/address/', JSON.stringify(additionalAddress));
        return response;
    }

    public PropertyParcelDetailsSave(property: PropertyParcel) {
        const response = this.httpPost(this._serviceURL + 'property/parceldetails', JSON.stringify(property));
        response.subscribe(result => console.log(result), error => console.log(error));
        return response;
    }

    public PropertyParcelDetailsDelete(parcel: PropertyParcel) {
        const response = this.httpPost(this._serviceURL + "/property/Parcel/", JSON.stringify(parcel))
        response.subscribe(result => console.log(result), error => console.log(error));
        return response;
    }
    public getPropertyResearchStatusFromSummary(PropertyID: any) {
        const response = this.httpGet(this._serviceURL + 'property/researchStatus/fromSummary/' + PropertyID);
        return response;
    }
    public savePropertyAlloction(propertyAlloction) {
        const response = this.httpPost(this._serviceURL + 'property/propertyAlloction', JSON.stringify(propertyAlloction));
        return response;
    }
    public GetPropertyAllocation(propertyID: number) {
        const response = this.httpGet(this._serviceURL + 'property/propertyAlloction/' + propertyID);
        return response;
    }
    public GetPropertyDetails(propertyId: number) {
        const response = this.httpGet(this._serviceURL + 'property/propertyDetails/' + propertyId);
        return response;
    }
    public GetPropertyAdditionalUses(parentId: number, parentTableId: number) {
        const response = this.httpGet(this._serviceURL + 'property/additionalUse/' + parentId + '/' + parentTableId , { applicationID: EnumApplication.VST });
        return response;
    }
    public savePropertyAdditionalUse(additionalUse) {
        const response = this.httpPost(this._serviceURL + 'property/additionalUse', JSON.stringify(additionalUse));
        return response;
    }
    public GetChangeLog(parentId, type, entityID) {
        const response = this.httpGet(this._serviceURL + 'property/getChangeLog/' + parentId + '/' + type + '/' + entityID);
        return response;
    }
    public SaveNeedsResearchInformation(needsResearchInformation) {
        const response = this.httpPost(this._serviceURL + 'property/needsResearchInformation', JSON.stringify(needsResearchInformation));
        return response;
    }
    public propertyCRESearch(searchCriteria: any) {
        const response = this.httpPost(this._serviceURL + 'property/VSTSearch/', JSON.stringify(searchCriteria));
        return response;
    }
    public saveMultiStrata(data) {
        const response = this.httpPost(this._serviceURL + 'property/Multistrata/', JSON.stringify(data));
        return response;
    }
    public saveAuditStatus(data) {
        const response = this.httpPost(this._serviceURL + '/property/auditstatus/save', JSON.stringify(data));
        return response;
    }
    public getFreeHoldPropertyDetails(searchText, isMaster) {
        searchText = this.encodeValue(searchText);
        const response = this.httpGet(this._serviceURL + '/lookup/MasterFreeholdPropertyDetails/' + searchText + '/' + (!!isMaster ? 1 : 0));
        return response;
    }

    public getPropertyFreeholdDetails(property_Id: any, SortBy: any, SortDirection: any) {
        const response = this.httpGet(this._serviceURL + 'property/freeholdDetails/' + property_Id + '/' + SortBy + '/' + SortDirection);
        return response;
    }

    public getPropertyIntersectMarketSubmarket(MetroID: number,
        PropertyID: number,
        UseTypeID: number,
        Latitude: number,
        Longitude: number) {
        const market = {
            MetroID: MetroID ||
                null, PropertyID: PropertyID ||
                    null, UseTypeID: UseTypeID ||
                        null, Latitude: Latitude ||
                            null, Longitude: Longitude ||
                                null
        };
        const response = this.httpPost(this._serviceURL + 'property/PropertyIntersectMarketSubmarket/', JSON.stringify(market));
        return response;
    }

    public GetSaleTransactions(data: any) {
        const response = this.httpPost(this._serviceURL + 'property/saleTransactions', JSON.stringify(data));
        return response;
    }

    public saleAutoEnrich(data: ISaleAutoEnrichRequest) {
        const response = this.httpPost(this._serviceURL + 'property/sale-auto-enrich', data);
        return response;
    }
}
