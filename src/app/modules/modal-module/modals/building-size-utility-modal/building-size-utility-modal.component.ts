import { Component, Input, Output, OnInit, EventEmitter, ElementRef, ViewChild, SimpleChanges } from '@angular/core';

//Imports from deck.gl
import { GoogleMapsOverlay } from '@deck.gl/google-maps';

import { Property } from '../../../../models/Property';
import { MapService } from '../../../../modules/map-module/service/map-service.service';
import { MapOptions } from '../../../../modules/map-module/models/MapOptions';
import * as MapEnum from '../../../../modules/map-module/models/MapEnum';
import { CommunicationService, CommunicationModel } from '../../../../services/communication.service';
import { Subscription } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

import { SharedDataService } from '../../../../services/shareddata.service';
import { Overlays } from '../../../../enumerations/overlays';
import { BuildingFootPrintService } from '../../../../services/building-footprint.service';
import { MapBound } from '../../../map-module/models/mapBound';
import { LatLng } from '../../../map-module/models/LatLng';
import { MapHelperService } from '../../../../services/map-helper.service';
import { NotificationService } from '../../../notification/service/notification.service';
import { LoginService } from '../../../../services/login.service';
import { PropertyService } from '../../../../services/api-property.service';
import { EnumCondoTypeName } from '../../../../enumerations/condoType';
import { masterStrataDTO } from '../../../../DTO/mapEditPropertyDTO';
import { environment } from './../../../../../environments/environment';
import { AddFloorService } from '../../../../services/add-floor.service';
import { MultiPolygon, CondoTypeChangeListenerType } from '../../../../models/Common';
import { MetaDataCollectionKeys } from '../../../../enumerations/indexeddb';
import { CommonStrings } from '../../../../constants';
import { ResearchType } from '../../../../enumerations/researchType';
import { PropertyResearchStatus } from '../../../../models/PropertyResearchStatus';
import { arrayToString,hasCommonValue, removeElementFromList, updateParcelInfo } from '../../../../utils';
import { EnumApplication } from '../../../../enumerations/application';
import { PropertyParcel } from '../../../../models/PropertyParcel';

declare var google: any;
@Component({
  selector: 'app-building-size-utility-modal',
  templateUrl: './building-size-utility-modal.component.html',
  styleUrls: ['./building-size-utility-modal.component.scss']
})
export class BuildingSizeUtilityModalComponent {
  @ViewChild('aerialViewCapture') aerialViewCapture: ElementRef;
  @Input() selectionEnabled;
  @Input() selectedCamera;
  @Input() isBuildingModalInFullScreenMode;
  @Input() isExpressMapInFullScreenMode;
  @Input() isStreetView;
  @Input() isBuildingModalStreetViewVisible;
  @Input() propertyDetails: Property = new Property();
  @Input() condoType: EnumCondoTypeName;
  @Input() masterStrataObj:masterStrataDTO = new masterStrataDTO();
  @Input() isFreeholdOrStrata:boolean = false;
  @Output() onClose = new EventEmitter();
  @Output() onSave: EventEmitter<Array<Property>> = new EventEmitter<Array<Property>>();
  @Output() buildingModalCameraClick = new EventEmitter<{aerialViewCaptureDiv: ElementRef }>();
  @Output() buildingModalFullScreenMode =new EventEmitter();
  @Output() buildingModalExitFullScreenMode =new EventEmitter();
  @Output() newFloorFootprint = new EventEmitter<{polygon: any, floorPolygon: any}>();
  @Output() updateBuildingModalStreetView = new EventEmitter<{isStreetViewVisible:boolean}>();
  @Output() updateNewLatLng = new EventEmitter<LatLng>();
  @Output() saveOldFootprint = new EventEmitter<{polygons: any}>();
  list : any[]= [
    {checked :true,id:Overlays.Parcel},
  ];
  deckOverlay: any;
  checkedList = [Overlays.Parcel];
  @Output() showDeleteShapeModal : EventEmitter<number> = new EventEmitter<number>();
  private propertyMarkers: Array<any>;
  private markerCluster: any;
  public mapOptions: MapOptions;
  public isShapeOnMap: boolean = false;
  public isSelectDrawPolyGon: boolean = false;
  maskOptions_precision_two: any;
  public map: any;
  public visible: boolean = false;
  drawingManager: any;
  public areaSM: any;
  public areaSF: any;
  NewPropertySelectedSubscription: Subscription;
  deleteBuildingFootprint: Subscription;
  clearPolygonShapeSubscription: Subscription;
  buildingsMapListener: Subscription;
  newBuildingFootprintAddedForProperty: Subscription;
  updatedBuildingFootprintAddedForProperty: Subscription;
  propertyRelocationListner: Subscription;
  propertyUseTypeUpdateListener: Subscription;
  floorInfoChangeListener: Subscription;
  CondoTypeChangeListener: Subscription;
  AverageEstimationEnabledListener
  deleteFloorListener: Subscription;
  mediaUploadedListener: Subscription;
  selectParcelsListener: Subscription;
  AddingNewFloor: Subscription;
  zoomLevel = 19;
  buildFootprints: any;
  buildingFootPrintIdToBeDeleted: number;
  selectedPropertyMarker: any;
  saveInfowindow: any;
  selectedPropertyID = undefined;
  selectedPropertyFloors = undefined;
  CountryId = undefined;
  polygon = undefined;
  newBuildingFootprint;
  editedFootprintID = undefined;
  floorPolygon = undefined;
  buildingFootPrintsData: any;
  buildingFootPrintsDataCopy: any;
  newLatLng: any;
  isFloorBtnDisable: boolean = false;
  showDeletePolygonModal: boolean=false;
  title: string;
  isPolygonEdited = false;
  EnumCondoTypeNames = EnumCondoTypeName;
  enableAzureMap = environment.enableAzureMap;
  floorLabels: boolean = false;
  floorsizeArea: any = [];
  multiFloors: MultiPolygon[] = [];
  selectedFloorID = undefined;
  showAllPolygons: boolean = false;
  isFullScreen: boolean = false;
  polygons: any;
  propertyUseTypeID = undefined
  private _propertyService: PropertyService;
  private _notificationService: NotificationService;
  isClearPolygon: boolean = false;
  propertyResearchStatus: PropertyResearchStatus;
  isHiddenPropertySelected: boolean = false;
  hiddenPropertyId: any = 0;
  isPropertyFromDifferentParcel: boolean = false;
  unHidePropertyMessage = CommonStrings.DialogConfigurations.Messages.UnHidePropertyMessage;
  propertyFromDifferentParcel = CommonStrings.DialogConfigurations.Messages.PropertyFromDifferentParcel;
  addBuildingsToMasterConfirmationMsg = '';  
  selectedPropertyIdsToMapToMaster: number[] = [];
  addBuildingsToMaster: boolean = false;
  parcelInfo: any;
  buildingIdsToUpdateParcel: any[] = [];
  masterParcelInfoFromTiles: any;
  masterParcelNoFromTiles: any;
  masterParcelFromDBAndTiles: any;
  isDefaultFootprintEdited = false;
  fromSelectParcels = false;
  existingPropertyParcels = [];
  showSelectedExistingParcelMessage = false;
  selectedParcels: any[] = [];
  showMultiParcelConfirmationModal = false;
  isAverageEstimationEnabled
  constructor(private _mapService: MapService,
  private _mapHelperService: MapHelperService,
    private addFloorService: AddFloorService,
    private _communicationService: CommunicationService,
    private sharedDataService: SharedDataService,
    private buildingFootprintService: BuildingFootPrintService,
    notificationService: NotificationService,
    private _loginService: LoginService,
    propertyService: PropertyService) {
    this._notificationService = notificationService;
    this.maskOptions_precision_two = { prefix: '' };
    this.CountryId = this._loginService.UserInfo.CountryId;
    this._propertyService = propertyService;
    this.NewPropertySelectedSubscription = this._communicationService.subscribe('NewPropertySelected').subscribe(result => {
      if (!this.map) {
        this.initMap();
        this.initializeData();
      }

      if (!!result.data && !!this.map) {
        this.newLatLng = undefined;
        this.selectedPropertyMarker = this._mapService.ClearSingleMarker(this.selectedPropertyMarker);
        let mapData = result.data;
        this._mapService.SetCenter(this.map, mapData.Latitude, mapData.Longitude);
        this._mapService.SetMapZoomLevel(this.map, 19);
        this.selectedPropertyMarker = this._mapService.PlaceMarker(this.map, mapData.Latitude, mapData.Longitude);
        this._mapService.OnMarkerDragEnd(this.selectedPropertyMarker, (latlng) => {
          this.newLatLng = latlng;
        })
        this.clearShapes();
        this.selectedPropertyID = mapData.PropertyID;
        this.propertyUseTypeID = mapData.UseTypeID;
        this.selectedPropertyFloors = mapData.Floors;
        this.condoType = mapData.CondoTypeID;
        this.fetchBuildingFootPrints(this.selectedPropertyID);
      }
    });
    this.deleteBuildingFootprint = this._communicationService.subscribe('deleteBuildingFootprint').subscribe(result => {
      if (!!result.data && !!this.map) {
        this.fetchBuildingFootPrints(this.selectedPropertyID);
      }
    });
    this.buildingsMapListener = this._communicationService.subscribe('mapBuildingsToMaster').subscribe(result => {
      if (!!result && !!this.map) {
        this.parcelInfo = result.data;
        this.goBuildingModalFullScreen();
        this.fetchViewPortProperties();
        if (!this.checkedList.includes(Overlays.Parcel)) {
          this.checkedList.push(Overlays.Parcel);
          const parcelLayer = this.list.find(overlay => overlay.id === Overlays.Parcel);
          if (parcelLayer) {
            parcelLayer.checked = true;
          }
        }
        this._mapService.addOverlay(this.checkedList, this.map, this.deckOverlay, 'layerInfoCardInBuildingModal');
        const currentCenter = this.map.getCenter();
        if (currentCenter) {
          this._mapService.SetCenter(this.map, currentCenter.lat(), currentCenter.lng()); // This will move the map slightly and trigger the viewport change event
        }
      }
    });
    this.clearPolygonShapeSubscription = this._communicationService.subscribe('ClearPolygonShape').subscribe(result => {
      this.selectedPropertyMarker = this._mapService.ClearSingleMarker(this.selectedPropertyMarker);
        this.clearShapes();
        this.buildFootprints = this._mapService.ClearPolygons(this.buildFootprints);

    });
    this.newBuildingFootprintAddedForProperty = this._communicationService.subscribe('newBuildingFootprintAddedForProperty').subscribe(result => {
      if (!!result.data) {
        this.visible = false;
        this.clearShapes();
      }
    });
    this.updatedBuildingFootprintAddedForProperty = this._communicationService.subscribe('updatedBuildingFootprintAddedForProperty').subscribe(result => {
      if (!!result.data) {
        this.visible = false;
        this.clearShapes();
      }
    });
    this.propertyRelocationListner = this._communicationService.subscribe('propertyRelocated').subscribe(result => {
      if(!!this.selectedPropertyMarker) {
        const position = new google.maps.LatLng(result.data.Latitude, result.data.Longitude);
        this.selectedPropertyMarker.position = position;
      }
    });

    this.propertyUseTypeUpdateListener = this._communicationService.subscribe('propertyUseTypeUpdate').subscribe(result => {
      if (result && result.data) {
        if (result.data === 7) {
          this.sharedDataService.clearPolygon = true;
          this.floorLabels = false;
          this.deletePolygon();
        }
      }
    });

    this.floorInfoChangeListener = this._communicationService.subscribe('floorInfoChange').subscribe(result => {
      if (result.data) {
        this.multiFloors = JSON.parse(JSON.stringify(result.data));
      }
    })
    this.floorInfoChangeListener = this._communicationService.subscribe('copyPolygon').subscribe(result => {
      if (result.data) {
        if (result.data.shape) {
          result.data.shape = !result.data.shape.includes('MULTIPOLYGON') && !result.data.shape.includes('POLYGON') ? `POLYGON((${result.data.shape}))` : result.data.shape;
        }
        //fetching latlngs from the polygon shapw
        const buildFootprintsList = this._mapService.getLatLngListFromPolygon(result.data.shape);
        //drawing the polygon on map using latlngs
        const polygon = this._mapService.DrawPolygonsOnMap(this.map, buildFootprintsList, null, false, 'blue', true, (event) => this.preparedPloygonsData(event),  true);
        this.polygons[result.data.localBuildingFootPrintID] = polygon[0];
        this.polygons[result.data.localBuildingFootPrintID].setMap(this.map);
        this.polygons[result.data.localBuildingFootPrintID].setOptions({ editable: false, draggable: false, clickable: false });
        result.data.shape = !result.data.shape.includes("MULTIPOLYGON") ? result.data.shape.replace("POLYGON((", "").replace("))", "") : result.data.shape;
        //appending the polygon to the multifloor data
        this.multiFloors = JSON.parse(JSON.stringify([...this.multiFloors, result.data]));
        this.isPolygonEdited = true;
      }
    })
    this.CondoTypeChangeListener = this._communicationService.subscribe('CondoTypeChange').subscribe(result => {
      if (result && result.data) { 
        const data: CondoTypeChangeListenerType = result.data;
        this.isFloorBtnDisable= data?.isFloorBtnDisable;
        this.isAverageEstimationEnabled = data.isAverageEstimationEnabled;
        // Polygons are cleared only when the record type is Master Freehold or Master without average estimation.
        if (data?.Condo === EnumCondoTypeName.Master_Freehold || (data?.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled)) {
          this.sharedDataService.clearPolygon = true;
          this.deletePolygon();
        }
        if (data?.Condo === EnumCondoTypeName.Master_Freehold) {
          if (!this.checkedList.includes(Overlays.Parcel)) {
            this.checkedList.push(Overlays.Parcel);
            const parcelLayer = this.list.find(overlay => overlay.id === Overlays.Parcel);
            if (parcelLayer) {
              parcelLayer.checked = true;
            }
            this._mapService.addOverlay(this.checkedList,this.map,this.deckOverlay,'layerInfoCardInBuildingModal');
          }
        }
        if ((data?.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || data?.Condo === EnumCondoTypeName.Master_Freehold) {
          this.floorLabels = false;
          this.deletePolygon();
        }
      }else{
        this.isFloorBtnDisable= false;
      }
    })
    this.AverageEstimationEnabledListener = this._communicationService.subscribe('AverageEstimationEnabled').subscribe(result => {
      if (result.data) {
        this.isAverageEstimationEnabled = result.data;
      }
    })

    this.mediaUploadedListener = this._communicationService.subscribe('MediaUploaded').subscribe(result => {
      if (result.data) {
        const isMasterProperty = this.isMasterStrataOrFreeHold();
        let showDrawingControl = isMasterProperty ? !isMasterProperty : !(this.buildingFootPrintsData && this.buildingFootPrintsData.length > 0);
        showDrawingControl = this.selectedFloorID ? showDrawingControl : false;
        this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode ? showDrawingControl : false });
      }
    })

    this.selectParcelsListener = this._communicationService.subscribe('selectParcels').subscribe(result => {
      if (result.data && result.data.propertyId) {
        this.selectedPropertyID = result.data.propertyId;
        this.fromSelectParcels = true;
        this.existingPropertyParcels = result.data.parcelNos && result.data.parcelNos.length > 0 ? result.data.parcelNos : [];
        this.goBuildingModalFullScreen();
        if (!this.checkedList.includes(Overlays.Parcel)) {
          this.checkedList.push(Overlays.Parcel);
          const parcelLayer = this.list.find(overlay => overlay.id === Overlays.Parcel);
          if (parcelLayer) {
            parcelLayer.checked = true;
          }
        }
        this._mapService.addOverlay(this.checkedList, this.map, this.deckOverlay, 'layerInfoCardInBuildingModal', false, (parcel) => this.saveParcels(parcel));
        this.showAllFootPrint();
      }
    })
  }

  saveParcels(parcels) {
    const parcelsSelected = [...this.selectedParcels];
    const selectedAnExistingParcel = parcels.find(parcel => this.existingPropertyParcels.includes(parcel.object.properties.Parcel_No));
    if (selectedAnExistingParcel) {
      this.showSelectedExistingParcelMessage = true;
      setTimeout(() => {
        this.showSelectedExistingParcelMessage = false;
      }, 1000);
    } else {
      if (parcels.length > 0) {
        parcels.forEach(parcel => {     
          const existingParcelIndex = parcelsSelected.findIndex(info => parcel.object.properties.Parcel_No === info.Parcel_No);
          if (existingParcelIndex === -1) {
            parcelsSelected.push(parcel.object.properties);
          } else {
            parcelsSelected.splice(existingParcelIndex, 1);
          }
        });
        setTimeout(() => {
          this.selectedParcels = [...parcelsSelected];
        }, 500);
      }
    }
    
  }

  initializeData() {
    const instance = this;
    this.sharedDataService.AzureMapURL = environment.AzureMapProxyURL
    if (!this.propertyDetails.Latitude) {
      this.propertyDetails.Latitude = -37.814;
      this.propertyDetails.Longitude = 144.96332;
    } else {
      this.zoomLevel =  19;

    }
    this.selectedPropertyMarker = this._mapService.ClearSingleMarker(this.selectedPropertyMarker);
    document.addEventListener('fullscreenchange', function(_) {
      if(instance.isFullScreen) {
        instance.exitBuildingModalFullScreen();
      }
      instance.isFullScreen = !instance.isFullScreen;
    });
    document.addEventListener("keydown", function(event) {
      const key = event.key;
      if (key === "Escape") {
        instance.clearShapes();
      }
    });
    if(this.sharedDataService.IsAzureMapOn){
      this._mapService.addAzureMapOverlay(this.map,this.checkedList, this.deckOverlay)
    }

    this.addFloorService.getValue().subscribe((value) => {
      this.floorLabels = value;
    });

    this.deleteFloorListener = this._communicationService.subscribe('deleteFloor').subscribe(result => {
      if (result.data) {
        this.multiFloors = result.data.multifloors;
        if(this.polygons && this.polygons[`${result.data.floorId}`]) {
          const polyg = this.polygons[`${result.data.floorId}`]
          polyg.setMap(null);
          delete this.polygons[result.data.floorId];
          this.saveEventsToLocalStorage();
        }
      }
    })
  }

  saveEventsToLocalStorage() {
    const dataToSave = {};
    // Extract and serialize the relevant parts of each event
    for (const key in this.polygons) {
      const polygonPath = this.polygons[key].getPath().getArray().map(latLng => ({
        lat: latLng.lat(),
        lng: latLng.lng()
      }));
      dataToSave[key] = { type: this.polygons[key].type, path: polygonPath };
    }
    // Store the serialized object in local storage
    localStorage.setItem(MetaDataCollectionKeys.MultiFloorPolygons, JSON.stringify(dataToSave));
  }

  loadEventsFromLocalStorage(map) {
    const jsonString = localStorage.getItem(MetaDataCollectionKeys.MultiFloorPolygons);
    if (!jsonString) {
      return null;
    }
    const storedData = JSON.parse(jsonString);
    const drawnPolygons = {};
    for (const key in storedData) {
      // Reconstruct the polygon on the map
      const polygon = new google.maps.Polygon({
        paths: storedData[key].path.map(coord => new google.maps.LatLng(coord.lat, coord.lng)),
        map: map,
        strokeColor: '#FF3333',
        strokeOpacity: 1.0,
        strokeWeight: 3,
        suppressUndo: true,
        fillColor: 'transparent',
      });
      google.maps.event.addListener(polygon.getPath(), 'set_at', () => {
        this.preparedPloygonsData(polygon);
      });
      google.maps.event.addListener(polygon.getPath(), 'insert_at', () => {
        this.preparedPloygonsData(polygon);
      });
      // Store the drawn polygon in a new object
      drawnPolygons[key] = polygon;
    }

    return drawnPolygons;
  }

  ngOnDestroy(): void {
    this.sharedDataService.AzureMapURL = environment.AzureMapBaseURL
    this.NewPropertySelectedSubscription.unsubscribe();
    this.deleteBuildingFootprint.unsubscribe();
    this.clearPolygonShapeSubscription.unsubscribe();
    this.newBuildingFootprintAddedForProperty.unsubscribe();
    this.buildingsMapListener.unsubscribe();
    this.updatedBuildingFootprintAddedForProperty.unsubscribe();
    this.propertyRelocationListner.unsubscribe();
    this.propertyUseTypeUpdateListener && this.propertyUseTypeUpdateListener.unsubscribe();
    this.floorInfoChangeListener && this.floorInfoChangeListener.unsubscribe();
    this.deleteFloorListener && this.deleteFloorListener.unsubscribe();
    this.mediaUploadedListener && this.mediaUploadedListener.unsubscribe();
    this.selectParcelsListener && this.selectParcelsListener.unsubscribe();
    this.AverageEstimationEnabledListener?.unsubscribe();
    this.CondoTypeChangeListener?.unsubscribe();
  }
  ngAfterViewInit() {
    //  this.NewPropertySelectedSubscription.unsubscribe();
  }
  private initMap() {
    this.mapOptions = new MapOptions('PropertyMap');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 9, 7, null, this.propertyDetails.Latitude, this.propertyDetails.Longitude);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.FullscreenControl = false;
    this.mapOptions.ZoomLevel = this.zoomLevel;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.ArterialRoad, MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.LocalRoad, MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    //initializing street view map
    const instance = this;
    google.maps.event.addListener(this.map.getStreetView(), 'visible_changed', function () {
      instance.updateBuildingModalStreetViewStatus();
    });
    this.polygons = this.loadEventsFromLocalStorage(this.map);
    this.selectedPropertyMarker = this._mapService.PlaceMarker(this.map, this.propertyDetails.Latitude, this.propertyDetails.Longitude);
    this.deckOverlay = new GoogleMapsOverlay({
      layers: []
    });
    this._mapService.addOverlay(this.checkedList,this.map,this.deckOverlay,'layerInfoCardInBuildingModal');
    this.DrawShape('polygon');
    //Disable the default full screen expand and full screen compress controls in street view
    this.map.get('streetView')
    .setOptions({
      fullscreenControl:false
    })
  }

  addControllers() {
    this._mapService.AddController(this.map,"dropdownLayers",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"aerialVieCam",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"fullScreenExpandBtn",MapEnum.GoogleMapControlPosition.Right_Top);
    this._mapService.AddController(this.map,"fullScreenCompressBtn",MapEnum.GoogleMapControlPosition.Right_Top);
    this._mapService.AddController(this.map,"saveAndCloseInBM",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map, "clearShapesInBM", MapEnum.GoogleMapControlPosition.Top_Left);
    this._mapService.AddController(this.map, "clearAllShapesInBM", MapEnum.GoogleMapControlPosition.Top_Left);
    this._mapService.AddController(this.map,"layerInfoCardInBuildingModal",MapEnum.GoogleMapControlPosition.LEFT_Top);
    this._mapService.AddController(this.map,"floorlevelbuilding",MapEnum.GoogleMapControlPosition.Right_Center);
    this._mapService.AddController(this.map,"selectedParcelsInfo",MapEnum.GoogleMapControlPosition.Left_Center);
    this._mapService.AddController(this.map,"parcelsWarningMessage",MapEnum.GoogleMapControlPosition.Top_Left);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(!(changes.isBuildingModalInFullScreenMode && changes.isBuildingModalInFullScreenMode.currentValue)) {
      this.setEditActionsForFootprints(false);
      if(this.selectedPropertyMarker){
      this.selectedPropertyMarker.gmpDraggable = false;
      }
      if(this.drawingManager){
      this.drawingManager.setOptions({ drawingControl: false });
      }
    }
  }

  isMasterStrataOrFreeHold() {
        // Treat both Master Freehold and Master without average estimation as "Master"
    // since footprints are not allowed in either of these cases
    return (this.condoType === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.condoType === EnumCondoTypeName.Master_Freehold || (this.masterStrataObj && this.masterStrataObj.isMultiStrata);
  }

  updateBuildingModalStreetViewStatus() {
    let isBuildingModalStreetViewVisible = this.map.getStreetView().getVisible();
    this.updateBuildingModalStreetView.emit({isStreetViewVisible:isBuildingModalStreetViewVisible});
  }

  shareCheckedList(item:string[]){
    this.hideTooltip();
    this._mapService.addOverlay(item,this.map,this.deckOverlay,'layerInfoCardInBuildingModal',this.isBuildingModalInFullScreenMode);
    }

  onBuildingModalCameraClick(): void {
    this.buildingModalCameraClick.emit({
    aerialViewCaptureDiv: this.aerialViewCapture,
  });
  }

  addMapOverlay(){
    this._mapService.addOverlay(this.checkedList,this.map,this.deckOverlay,'layerInfoCardInBuildingModal');
  }

  goBuildingModalFullScreen(): void {
    this._mapService.addOverlay(this.checkedList,this.map,this.deckOverlay,'layerInfoCardInBuildingModal',true);
    this.buildingModalFullScreenMode.emit();
    if(this.selectedPropertyMarker){
     this.selectedPropertyMarker.gmpDraggable = true;
    }
    this.polygons && Object.values(this.polygons).forEach((polygon: any) => {
      polygon.setMap(null);
    });
    const floorsWithoutPolygons = this.multiFloors.filter(floor => !floor.floorSize && floor.minFloor && floor.maxFloor);
    const isMasterProperty = this.isMasterStrataOrFreeHold();
    // Show drawing control only if the property is not master strata or freehold
    this.drawingManager.setOptions({
      drawingControl: !isMasterProperty && floorsWithoutPolygons.length > 0
    });
    if (floorsWithoutPolygons.length > 0 && !this.isBuildingModalStreetViewVisible) {
      const { BuildingFootPrintID, localBuildingFootPrintID } = floorsWithoutPolygons[0];
      // Select the appropriate floor ID based on default or available footprint IDs
      this.selectedFloorID = BuildingFootPrintID
          ? BuildingFootPrintID.toString()
          : localBuildingFootPrintID;
    }
    if (this.selectedFloorID && this.multiFloors.length > 0 && this.polygons) {
      if (this.polygons && this.polygons[`${this.selectedFloorID}`]) {
        const polyg = this.polygons[`${this.selectedFloorID}`]
        polyg.setMap(this.map);
        polyg.setOptions({ editable: true, draggable: true, clickable: true });
        this.drawingManager.setOptions({
          drawingControl: false
        });
      }
    }
  }

  // Hide the existing Parcel info card on deselecting Parcel layer
  hideTooltip = () => {
    const tooltip = document.getElementById('layerInfoCardInBuildingModal');
    if (tooltip) {
      tooltip.innerHTML = `<div></div>`;
    }
  }

  exitBuildingModalFullScreen(): void {
    this.hideTooltip();
    this.addMapOverlay();
    this.fromSelectParcels = false;
    this.selectedParcels = [];
    this.buildingModalExitFullScreenMode.emit();   
    if(this.selectedPropertyMarker){
      this.selectedPropertyMarker.gmpDraggable = false;
    }    
    this.drawingManager.setOptions({ drawingControl: false });
    this.setEditActionsForFootprints(false);
    // Ensure this.polygons is defined
    if (this.polygons) {
      const fullScreen = Object.values(this.polygons);
      
      // Check if fullScreen is not empty
      if (fullScreen.length > 0) {
        fullScreen.forEach((polygon: any) => {
          polygon.setMap(this.map);
          polygon.setOptions({ editable: false, draggable: false, clickable: false });
        });
      }
    }    
    const multiFloors = this.multiFloors.filter(floor => floor.minFloor && floor.maxFloor);
    this.selectedFloorID = multiFloors.length > 0 ? multiFloors[0].BuildingFootPrintID ? multiFloors[0].BuildingFootPrintID.toString() : multiFloors[0].localBuildingFootPrintID : undefined; 
    this.showAllPolygons = false;
  }

  saveAndClose(): void {
    if(this.selectedPropertyIdsToMapToMaster && this.selectedPropertyIdsToMapToMaster.length > 0){
    const selectedPinsString = arrayToString(this.selectedPropertyIdsToMapToMaster);
    this.addBuildingsToMasterConfirmationMsg = this.addBuildingsToMasterConfirmationMsg = `You are about to convert the <b>${this.selectedPropertyIdsToMapToMaster.length}</b> PID(s) <b>${selectedPinsString}</b> to Freehold Child Pins. Do you wish to continue?`;
    this.addBuildingsToMaster = true;
    } else if(this.fromSelectParcels && this.selectedParcels.length > 0) {
      this.showMultiParcelConfirmationModal = true;
    } else{
     this.onComplete();
    }
  }

  onComplete(){
    this.hideTooltip();
    this.addMapOverlay();
    if (this.polygons && this.polygons[this.selectedFloorID]) {
      this.polygons[this.selectedFloorID].setOptions({ editable: false, draggable: false, clickable: false });
    }
    this.buildingModalExitFullScreenMode.emit();
    this.drawingManager.setOptions({ drawingControl: false });
    if(this.selectedPropertyMarker){
      this.selectedPropertyMarker.gmpDraggable = false;
    }
    this.setEditActionsForFootprints(false);
    this.updateNewLatLng.emit(this.newLatLng);
    this.showAllPolygons = false;
    if (this.isPolygonEdited || this.isClearPolygon || this.isDefaultFootprintEdited) {
      this.saveEventsToLocalStorage();
      this.onSaveBuildingSize();
      let commModel = new CommunicationModel();
      commModel.Key = 'floorShapeChange';
      commModel.data = { multiFloors: this.multiFloors, isUpdated: true, floordeleted: this.isClearPolygon };
      this._communicationService.broadcast(commModel); 
    }
    this.clearMarkersData();
    this.fromSelectParcels = false;
    this.selectedParcels = [];
  }

  showAllFootPrint() {
    this.drawingManager.setOptions({ drawingControl: false });
    if(this.showAllPolygons) {
      this.showAllPolygons = false;
      this.selectedFloorID = undefined;
      this.polygons && Object.values(this.polygons).forEach((polygon: any) => {
        polygon.setMap(null);
      });
    } else {
      this.showAllPolygons = true;
      this.selectedFloorID = undefined;
      this.polygons && Object.values(this.polygons).forEach((polygon: any) => {
        polygon.setMap(this.map);
        polygon.setOptions({ editable: false, draggable: false, clickable: false });
      });
    }
  }

  selectFloor(buildingId, localId) {
    const index = buildingId ? buildingId.toString() : localId;
    if(this.showAllPolygons) {
      this.showAllPolygons = false;
      this.selectedFloorID = undefined;
      this.polygons && Object.values(this.polygons).forEach((polygon: any) => {
        polygon.setMap(null);
      })
    }
    if (this.selectedFloorID && this.selectedFloorID !== index) {
      if (this.polygons && this.polygons[`${this.selectedFloorID}`]) {
        const polyg = this.polygons[`${this.selectedFloorID}`]
        polyg.setMap(null)
      }
    }
    if (this.selectedFloorID === index) {
      this.selectedFloorID = undefined;
      this.drawingManager.setOptions({ drawingControl: false });
      if (this.polygons && this.polygons[`${index}`]) {
        const polyg = this.polygons[`${index}`]
        polyg.setMap(null);
      }
    } else {
      this.selectedFloorID = index;
      if (this.polygons && this.polygons[`${index}`]) {
        const polyg = this.polygons[`${index}`];
        polyg.setMap(this.map);
        polyg.setOptions({ editable: true, draggable: true, clickable: true });
        this.drawingManager.setOptions({ drawingControl: false});
      } else {
        this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode });
        this.setEditActionsForFootprints(false);
      }
    }
  }

  addFloor() {
    this.addFloorService.getValue().subscribe((value) => {
      this.floorLabels = value;
    });    
    this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode });
    this.setEditActionsForFootprints(false);
  }

  private clearShapes() {
    this.visible = false;
    this.areaSF = null;
    this.areaSM = null;
    this.isShapeOnMap = true;
    if (this.polygons) {
      for (const key in this.polygons) {
        this.polygons[key].setMap(null);
      }
      this.polygons = undefined;
    }
    this.isPolygonEdited = false;
    this.polygon = undefined;
    this.editedFootprintID = undefined;
  }

  DrawShape(shape) {
    let instance = this;
    this.isShapeOnMap = false;
    this.drawingManager = this._mapService.DrawPolygon(this.map, shape, undefined, 'blue');
    if (shape == "polygon") {
      this.isSelectDrawPolyGon = true;
      this._mapService.OnMapOverlayComplete(this.drawingManager, MapEnum.DrawMode.Polygon, (event) => {
        event.setOptions({ editable: true, draggable: true, clickable: true});
        this.drawingManager.setDrawingMode(null);
        this.drawingManager.setOptions({ drawingControl: false });
        this.visible = true;
        instance.polygons = { ...instance.polygons, [instance.selectedFloorID]: event };
        instance.polygons[this.selectedFloorID] = event;
        let latlngArray = event.latLngs.getArray()[0].getArray();
        var area = google.maps.geometry.spherical.computeArea(latlngArray);
        this.isPolygonEdited = true;
        this.areaSM = area;
        this.areaSF = area * 10.764;
        this.map.setOptions({ draggable: true });
        instance.isShapeOnMap = true;
        var polygonPath = event.getPath();
        var poly = [];
        var bounds = new google.maps.LatLngBounds();
        for (var i = 0; i < polygonPath.getLength(); i++) {
          var latLng = polygonPath.getAt(i);
          poly.push(`${latLng.lng()} ${latLng.lat()}`);
          const ll = new google.maps.LatLng(latLng.lat(), latLng.lng());
          bounds.extend(ll);
        }
        poly.push(`${polygonPath.getAt(0).lng()} ${polygonPath.getAt(0).lat()}`)
        // this.polygon = poly.join(',');
        this.multiFloors.forEach((floor, index) => {
          if (this.currentTab(floor.BuildingFootPrintID, floor.localBuildingFootPrintID)) {
            floor.shape = poly.join(',');
            floor.floorSize = instance.areaSM.toFixed(2);
          }
        })
        google.maps.event.addListener(event.getPath(), 'set_at', () => {
          instance.preparedPloygonsData(event);
        });

        // Add event listener for new vertex insertion
        google.maps.event.addListener(event.getPath(), 'insert_at', () => {
          instance.preparedPloygonsData(event);
        });
      });
    }
  }

  preparedPloygonsData(event, isEdited = false) {
    this.isPolygonEdited = true;
    this.polygons = { ...this.polygons, [this.selectedFloorID]: event };
    const newArea = google.maps.geometry.spherical.computeArea(event.getPath());
    this.areaSM = newArea;
    this.areaSF = newArea * 10.764;
    var latlongs = [];
    var newLatLng = undefined;
    for (var i = 0; i < event.getPath().getLength(); i++) {
      var latLng = event.getPath().getAt(i);
      latlongs.push(`${latLng.lng()} ${latLng.lat()}`);
    }
    latlongs.push(latlongs[0]);
    newLatLng = latlongs.join(',').replace("'", '');
    this.multiFloors.forEach((floor, index) => {
      if (this.currentTab(floor.BuildingFootPrintID, floor.localBuildingFootPrintID)) {
        floor.shape = newLatLng;
        floor.floorSize = this.areaSM.toFixed(2);
      }
    });
  }

  setEditActionsForFootprints(isEditable) {
    if (this.buildFootprints && this.buildFootprints.length > 0 ) {
      this.buildFootprints.forEach(footprint => {
        footprint.setEditable(isEditable);
        footprint.setDraggable(isEditable);
          footprint.setOptions({clickable : isEditable });
      });
    }
  }

  formatPropertyFootprints(footprints, polygons) {
    this.polygons = this.polygons ? this.polygons : {};
    this.multiFloors = [];
    footprints.forEach((print, index) => {
      const polygonIndex = print.BuildingFootPrintID ? print.BuildingFootPrintID : print.localBuildingFootPrintID;
      this.polygons[polygonIndex] = polygons[index];
      this.multiFloors[index] = {
        specificUse: print.UseTypeId,
        maxFloor: print.PropertyMaxFloor ? parseInt(print.PropertyMaxFloor) : null,
        minFloor: print.PropertyMinFloor ? parseInt(print.PropertyMinFloor) : null,
        floorCount: print.Floors ? parseInt(print.Floors): null,
        floorSize: parseFloat(print.SizeInSM),
        shape: !print.BuildingFootPrint.includes("MULTIPOLYGON") ? print.BuildingFootPrint.replace("POLYGON((", "").replace("))", "") : print.BuildingFootPrint,
        BuildingFootPrintID: print.BuildingFootPrintID,
        description: print.Description,
        additionalUse: print.AdditionalUseTypeId,
        additionalSpecificUseTypeId: print.AdditionalSpecificUseTypeId,
        mainSpecificUseTypeId: print.MainSpecificUseTypeId,
        localBuildingFootPrintID: print.BuildingFootPrintID ? null : print.localBuildingFootPrintID
      }
    });
    if (this.multiFloors.length > 0){
      this.selectedFloorID = this.multiFloors[0].BuildingFootPrintID ? this.multiFloors[0].BuildingFootPrintID.toString() : this.multiFloors[0].localBuildingFootPrintID;
    } else {
      this.selectedFloorID = undefined;
    }
  }

  formatMultipolygonData(response): any[] {
    return response.flatMap((item) => {
      // Remove the "MULTIPOLYGON(" , ")" from the BuildingFootPrint
      const cleanedBuildingFootPrint = item.BuildingFootPrint.replace(/^MULTIPOLYGON\(/, '').replace(/\)$/, '');
     // Extract individual polygons using regex and represented in string format
      const polygons = cleanedBuildingFootPrint.split(/\),\(/);
      const buildFootprints = polygons.map((polygon) => {
        //removing all braces as data format varies
        const formattedPolygon = polygon.replace(/[()]/g, '');
        return {
          ...item,
          BuildingFootPrint: `POLYGON((${formattedPolygon}))`,
          BuildingFootPrintID: null,
          localBuildingFootPrintID: uuidv4(),
        };
      });
      return buildFootprints;
    });
  }  

  multiPolygonDisplay(element){
    const data = this.formatMultipolygonData(this.buildingFootPrintsData);
    data.forEach(polygon => {
      const latLngList = this._mapService.getLatLngListFromPolygon(polygon.BuildingFootPrint);
      this.buildFootprints = this._mapService.DrawPolygonsOnMap(this.map, latLngList, this.buildFootprints, false, 'blue', true, (event) => this.preparedPloygonsData(event), true);
      this.buildFootprints.forEach(footprint => {
        const paths = footprint.getPath();
        const newArea = google.maps.geometry.spherical.computeArea(paths);                  
        polygon.SizeInSM = newArea.toFixed(2);
        polygon.SizeInSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', Number(polygon.SizeInSM));
      });
    })
    this.formatPropertyFootprints(data ? data : [], this.buildFootprints);
    const deleteFootprint = [...this.sharedDataService.deleteBuildingFootPrintIds];
    if (!deleteFootprint.includes(element.BuildingFootPrintID)) {
      deleteFootprint.push(element.BuildingFootPrintID);
    }             
    this.sharedDataService.deleteBuildingFootPrintIds = deleteFootprint;
  }

  fetchBuildingFootPrints(propertyID) {
    this.multiFloors = [];
    this.buildFootprints = this._mapService.ClearPolygons(this.buildFootprints);
    this.buildingFootPrintsData = [];
    this.saveOldFootprint.emit({polygons: undefined});
    if(propertyID) {
      this.buildingFootprintService.getBuildingFootPrintsByPropertyID(propertyID).subscribe((result: any) => {
        let commModel = new CommunicationModel();
        commModel.Key = 'floorShapeChange';
        this.buildFootprints = this._mapService.ClearPolygons(this.buildFootprints);
        const response = result.body;
        if (response && response.responseData && response.responseData.length > 0) {
          this.buildingFootPrintsData = response.responseData;
          const oldFootPrints = this.buildingFootPrintsData && this.buildingFootPrintsData.length
            ? this.buildingFootPrintsData.reduce((acc, footprint) => ({
              ...acc,
              [footprint.BuildingFootPrintID]: {
                Floor: `${footprint.PropertyMinFloor}-${footprint.PropertyMaxFloor}`,
                footprint: footprint.BuildingFootPrint,
              }
            }), {})
            : {};
          this.saveOldFootprint.emit({ polygons: oldFootPrints });
          this.drawingManager.setOptions({ drawingControl: false });
          this.buildingFootPrintsData.forEach(element => {
            if (element.BuildingFootPrint.startsWith('MULTIPOLYGON')) {
              this.multiPolygonDisplay(element);
              commModel.data = { multiFloors: this.multiFloors, isUpdated: true, floordeleted: this.isClearPolygon = true};
            } else {
              const buildFootprintsList = this._mapService.getLatLngListFromPolygon(element?.BuildingFootPrint);
              this.buildFootprints = this._mapService.DrawPolygonsOnMap(this.map, buildFootprintsList, this.buildFootprints, false, 'blue', true, (event) => this.preparedPloygonsData(event),  true);
              this.formatPropertyFootprints(response.responseData ? response.responseData : [], this.buildFootprints);
              commModel.data = { multiFloors: this.multiFloors, isUpdated: false };      
            }
          });
          this.setEditActionsForFootprints(false);
        } else {
          this.multiFloors = [];
          this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode });
          commModel.data = { multiFloors: this.multiFloors, isUpdated: false };
        }
        this._communicationService.broadcast(commModel);
      })
    } else {
      this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode });
    }
  }

  deletePolygonConfirmation (shapeId) {
    this.buildingFootPrintIdToBeDeleted = shapeId;
    this.showDeleteShapeModal.emit(this.buildingFootPrintIdToBeDeleted)
  }

  onSaveBuildingSize() {
    this.onSave.emit(this.areaSM);
    this.onClose.emit();
  }

  deletePolygon() {
    this.isDefaultFootprintEdited = true;
    this.drawingManager.setOptions({ drawingControl: false });
    const currentIds = [...this.sharedDataService.deleteBuildingFootPrintIds];
    this.multiFloors.forEach(item => {
      if (!currentIds.includes(item.BuildingFootPrintID)) {
        currentIds.push(item.BuildingFootPrintID);
      }
    });
    this.sharedDataService.deleteBuildingFootPrintIds = currentIds.filter(id => id);
    this.buildFootprints = this._mapService.ClearPolygons(this.buildFootprints);
    this.polygon = undefined;
    this.visible = false;
    this.isPolygonEdited = true;
    this.buildingFootPrintsData = [];
    this.areaSM = 0;
    this.drawingManager.setOptions({ drawingControl: false });
    this.onSaveBuildingSize();
    if (this.polygons) {
      for (const key in this.polygons) {
        this.polygons[key].setMap(null);
      }
      this.polygons = undefined;
    }
    this.multiFloors.forEach(item => {
      item.floorSize = undefined;
      item.shape = undefined;
    })
    //filtering out all floors except for default floor
    this.showDeletePolygonModal = false;
  }

  close(){
    this.showDeletePolygonModal= false;
  }
  
  clearAllPolygons() {
    this.showDeletePolygonModal=true;
    this.title= CommonStrings.DialogConfigurations.Messages.CancelChangesConfirmationMessage;
  }

  clearPolygons() {
    this.isClearPolygon = true;
    var id = this.selectedFloorID;
    if (this.polygons) {
      this.polygons[this.selectedFloorID].setMap(null);
      delete this.polygons[this.selectedFloorID]
      const currentIds = [...this.sharedDataService.deleteBuildingFootPrintIds];
      //building footprint id
      const newId = !Number.isNaN(parseInt(id)) ? parseInt(id) : null;
      if (newId && !currentIds.includes(newId)) {
        //building ids that are to be deleted after save
        currentIds.push(newId);
      }
      this.sharedDataService.deleteBuildingFootPrintIds = currentIds;
    }
    this.multiFloors.map((item, index) => {
      if ((item.BuildingFootPrintID && item.BuildingFootPrintID.toString() === id) || (item.localBuildingFootPrintID && item.localBuildingFootPrintID === id)) {
        item.floorSize = undefined;
        item.shape = undefined;
      }
      return item;
    });
    this.drawingManager.setOptions({ drawingControl: this.isBuildingModalInFullScreenMode });
  }

  currentTab(buildingId, locaId) {
    if(buildingId) {
      return buildingId.toString() === this.selectedFloorID;
    } else {
      return  locaId === this.selectedFloorID;
    }
  }

  isPolygonsPresent() {
    const noOfPolygons = this.polygons ? Object.keys(this.polygons).length : 0;
    return noOfPolygons > 0 ? true : false;
  }

  private fetchViewPortProperties() {
    if (this.map) {
      const instance = this;
      this._mapService.OnMapViewPortChangedOnce(this.map, (boundProperties: MapBound) => {
        if(instance.isBuildingModalInFullScreenMode){
          const NWCorner = new google.maps.LatLng(boundProperties.NorthEast.Latitude, boundProperties.SouthWest.Longitude);
          const SECorner = new google.maps.LatLng(boundProperties.SouthWest.Latitude, boundProperties.NorthEast.Longitude);
          let mainPolyText = '(';
          mainPolyText += NWCorner.lng() + ' ' + NWCorner.lat() + ','
          mainPolyText += boundProperties.NorthEast.Longitude + ' ' + boundProperties.NorthEast.Latitude + ','
          mainPolyText += SECorner.lng() + ' ' + SECorner.lat() + ','
          mainPolyText += boundProperties.SouthWest.Longitude + ' ' + boundProperties.SouthWest.Latitude + ','
          mainPolyText += NWCorner.lng() + ' ' + NWCorner.lat() + ')'
          const polygonText = 'POLYGON(' + mainPolyText + ')';
          const properties = this._propertyService.getMapBoundProperties(
            boundProperties.SouthWest.Latitude,
            boundProperties.SouthWest.Longitude,
            boundProperties.NorthEast.Latitude,
            boundProperties.NorthEast.Longitude,
            polygonText);

          properties.subscribe(result => {
            const response = result.body;
            if (!response.error) {
              const res = response.responseData;
              instance.loadPropertyPins(res.PropertyList);
            }
            instance.fetchViewPortProperties();
          });
        } else {
          instance.clearMarkersData();
        }
        }
      );
    }
  }

  getParcelFromTiles(latitude, longitude, isMaster = false) {
    const lat = latitude
    const lng = longitude

    // Calculate the number of tiles based on the current zoom level
    const numTiles = 1 << this.map.getZoom();
    const projection = this.map.getProjection();

    // Create a LatLng object from the latitude and longitude
    const latLng = new google.maps.LatLng(lat, lng);

    // Convert the LatLng to world coordinates
    const worldCoordinate = projection.fromLatLngToPoint(latLng);
    const pixelCoordinate = new google.maps.Point(
        worldCoordinate.x * numTiles,
        worldCoordinate.y * numTiles
    );

    // Get the top-left corner coordinates of the current map view
    const topLeft = new google.maps.LatLng(
        this.map.getBounds().getNorthEast().lat(),
        this.map.getBounds().getSouthWest().lng()
    );

    // Calculate the pixel coordinates of the top-left corner
    const topLeftWorldCoordinate = projection.fromLatLngToPoint(topLeft);
    const topLeftPixelCoordinate = new google.maps.Point(
        topLeftWorldCoordinate.x * numTiles,
        topLeftWorldCoordinate.y * numTiles
    );

    // Calculate the relative pixel coordinates
    const relativePixelPoint = new google.maps.Point(
        pixelCoordinate.x - topLeftPixelCoordinate.x,
        pixelCoordinate.y - topLeftPixelCoordinate.y
    );

    // Use the relative pixel coordinates to fetch the parcel information from deckOverlay
    const pickedObjects = this.deckOverlay.pickObjects({
        x: relativePixelPoint.x,
        y: relativePixelPoint.y,
        layerIds: [Overlays.NotStrata] // Layer IDs to query
    });
    let parcelObjectProperties = pickedObjects[0];
    if (parcelObjectProperties) {
      if(isMaster){
        this.masterParcelInfoFromTiles = parcelObjectProperties.object.properties
      }
      return parcelObjectProperties.object.properties.Parcel_No;
    } else {
      return null;
    }

}


  private loadPropertyPins(propertyList) {
    this.markerCluster = this._mapService.clearCluster(this.markerCluster);
    this.propertyMarkers = this._mapService.ClearMarkers(this.propertyMarkers);
    let markers: Array<any> = new Array<any>();
    const bounds = new google.maps.LatLngBounds();
    for (let property of propertyList) {
      if (property.PropertyID !== this.selectedPropertyID && (property.CondoTypeId === EnumCondoTypeName.NotStrata || property.CondoTypeId === null)) {
        const icon = this._mapHelperService.GetPropertyPinByResearchType(property.PropertyResearchTypeID, property.GenUseId, property.SpecificUseID);
        let marker = this._mapService.PlaceMarker(this.map, property.Latitude, property.Longitude, false, icon);
        markers.push(marker);
        marker.data = property;
        this.propertyMarkers.push(marker);
        this.addPinAnimation(); 
        this._mapService.OnMarkerClick(marker, (event, m, latlng) => {
          if (this.selectedPropertyIdsToMapToMaster.includes(m.data.PropertyID)) {
            removeElementFromList(this.selectedPropertyIdsToMapToMaster, m.data.PropertyID);
            removeElementFromList(this.buildingIdsToUpdateParcel, m.data.PropertyID);
            this.addPinAnimation();
          } else {
            if (!this.masterParcelNoFromTiles) {
              this.masterParcelNoFromTiles = this.getParcelFromTiles(this.propertyDetails.Latitude, this.propertyDetails.Longitude,true);
              this.masterParcelFromDBAndTiles = updateParcelInfo(this.parcelInfo, this.masterParcelNoFromTiles);
            }
            const selectedBuildingParcelFromTiles = this.getParcelFromTiles(m.data.Latitude, m.data.Longitude);
            const buildingParcel = updateParcelInfo(m.data.ParcelInfo, selectedBuildingParcelFromTiles);
            const isParcelSame = hasCommonValue(buildingParcel, this.masterParcelFromDBAndTiles);
            if (!isParcelSame) {
              this.isPropertyFromDifferentParcel = true;
            } else if (m.data.PropertyResearchTypeID === ResearchType.Hidden) {
              this.onHiddenPropertySelection(m.data.PropertyID);
            } else {
              this.selectedPropertyIdsToMapToMaster.push(m.data.PropertyID);
              //Check whether the parcel of selected building is same as the master in the DB , if not then need to update the parcel for selected building
              const hasCommonDBParcel = hasCommonValue(m.data.ParcelInfo, this.parcelInfo);
              if (!hasCommonDBParcel) {
                this.buildingIdsToUpdateParcel.push(m.data.PropertyID);
              }
              this.addPinAnimation();
            }
          }
        });
      }

    }
    this.markerCluster = this._mapService.createCluster(this.map, markers, 15, 10);
    let set = new Set(markers);
    this.markerCluster.markers.forEach(item => set.add(item));
    markers = Array.from(set.values());
    this._mapService.onClusterClick(this.markerCluster, (cluster) => {
      if (this.markerCluster.isZoomOnClick()) {
        this.map.setCenter(cluster.getCenter())
      }
    });
  }

  private addPinAnimation(): void {
    for (const marker of this.propertyMarkers) {
      if (this.selectedPropertyIdsToMapToMaster.includes(marker.data.PropertyID)) {
        this._mapService.SetMarkerAnimation(marker, MapEnum.Animation.Bounce);
      } else {
        this._mapService.ClearMarkerAnimation(marker);
      }
    }
  }

  onHiddenPropertySelection(propertyId){
    this.isHiddenPropertySelected = true;
    this.hiddenPropertyId = propertyId;
}

  unHideProperty() {
    let fieldResearchCoimpleteStatus = null;
    const researchStatus = new PropertyResearchStatus();
    researchStatus.ApplicationID = EnumApplication.VST;
    researchStatus.IsNewProperty = false;
    researchStatus.PropertyResearchTypeID = ResearchType.FieldResearchComplete;
    researchStatus.EntityID = this._loginService.UserInfo.EntityID;
    researchStatus.PropertyID = this.hiddenPropertyId;
    researchStatus.IsActive = true;
    researchStatus.ResearchStatusPin = environment.MapIconFieldResearchComplete;;
    researchStatus.PropertyResearchTypeName = "Field Research Complete",
      researchStatus.Sequence = 4;
    let instance = this;
    const response_researchStatus = this._propertyService.getPropertyResearchStatus(this.hiddenPropertyId);
    response_researchStatus.subscribe(result => {
      if (!result.body.error) {
        let propResearchStatus = result.body.responseData[0];
        if (propResearchStatus) {
          fieldResearchCoimpleteStatus = propResearchStatus.find(status => status.PropertyResearchTypeID === ResearchType.FieldResearchComplete);
          if (fieldResearchCoimpleteStatus) {
            researchStatus.PropertyResearchStatusID = fieldResearchCoimpleteStatus.PropertyResearchStatusID || 0;
            researchStatus.ModifiedPersonName = fieldResearchCoimpleteStatus.ModifiedPersonName || '';
            researchStatus.ModifiedDate = fieldResearchCoimpleteStatus.ModifiedDate || '';
          }
          console.log("researchStatus",researchStatus);
          const response_researchStatus = this._propertyService.propertyResearchStatusSave(researchStatus);
          response_researchStatus.subscribe(result => {
            if (result.status === 200) {
              instance.selectedPropertyIdsToMapToMaster.push(instance.hiddenPropertyId);
              instance.hiddenPropertyId = 0;
              instance.isHiddenPropertySelected = false;
              const currentCenter = this.map.getCenter();
              if (currentCenter) {
                this._mapService.SetCenter(this.map, currentCenter.lat(), currentCenter.lng()); // This will move the map slightly and trigger the viewport change event
              }
            } else {
              this.clearHiddenPropertyData();
            }
          });
        } else {
          this.clearHiddenPropertyData();
        }
      } else {
        this.clearHiddenPropertyData();
      }
    })
  }

clearHiddenPropertyData(){
  this.hiddenPropertyId = 0;
  this.isHiddenPropertySelected = false;
}

closeUnhideConfirmationPopup(){
  this.hiddenPropertyId = 0;
  this.isHiddenPropertySelected = false; 
}

onParcelErrorPopupClose(){
  this.isPropertyFromDifferentParcel = false;
}

clearMarkersData(){
  this.selectedPropertyIdsToMapToMaster = [];
  this.addBuildingsToMaster = false;
  this.markerCluster = this._mapService.clearCluster(this.markerCluster);
  this.propertyMarkers = this._mapService.ClearMarkers(this.propertyMarkers);
}

onCancel(){
  this.onComplete();
}

onAddBuildingsToMaster(){
  //call API to add the buildings to master freehold
  const childPropertyIDS = arrayToString(this.selectedPropertyIdsToMapToMaster);
  const requestBody = {
    MasterPropertyID: this.selectedPropertyID,
    ChildPropertyIDS:childPropertyIDS,
    ApplicationID: EnumApplication.VST
  }
  const response = this._propertyService.linkChildsToMaster(requestBody);
  response.subscribe(result=>{
    this.onComplete();
    if(!result.body.error){
        this.updateChildFreeholdParcels();
      this._notificationService.ShowSuccessMessage(CommonStrings.SuccessMessages.LinkedChildToMasterSuccessfully);
      let commModel = new CommunicationModel();
      commModel.Key = 'fetchFreeholdList';
      this._communicationService.broadcast(commModel);
    }else{
      this._notificationService.ShowSuccessMessage(CommonStrings.ErrorMessages.ChildLinkingFailureMsg)
    }
  })
}


//Save the parcel information  associated with the master property
  savePropertyParcelInformation(propertyId) {
    if (this.masterParcelInfoFromTiles && propertyId) {
      const parcel = new PropertyParcel();
      parcel.ParcelNo = this.masterParcelInfoFromTiles.Parcel_No;
      parcel.Lot = this.masterParcelInfoFromTiles.Lot;
      parcel.ApplicationID = EnumApplication.VST;
      parcel.EntityID = this._loginService.UserInfo.EntityID;
      parcel.PropertyID = propertyId;
      parcel.ParcelSF = this.masterParcelInfoFromTiles.Lot_Area ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.masterParcelInfoFromTiles.Lot_Area): null;
      const response_parcel = this._propertyService.PropertyParcelDetailsSave(parcel);
      response_parcel.subscribe(result => {
        removeElementFromList(this.buildingIdsToUpdateParcel, propertyId)
        if (result.body && result.body.error) {
          this._notificationService.ShowErrorMessage(`Failed to update the parcel for ${propertyId}`);
        }
      });
    }
  }

  updateChildFreeholdParcels() {
    if (this.buildingIdsToUpdateParcel && this.buildingIdsToUpdateParcel.length > 0) {
      this.buildingIdsToUpdateParcel.forEach((building: any) => {
        this.savePropertyParcelInformation(building);
      })
    }
  }

  saveParcelsInfo() {
    if (this.selectedParcels && this.selectedParcels.length > 0) {
      this.selectedParcels.forEach(parcel => {
        const propertyParcel = new PropertyParcel();
        const parcelSize = parcel.Lot_Area ? parcel.Lot_Area : parcel.ParcelSF;
        propertyParcel.ParcelNo = parcel.Parcel_No ? parcel.Parcel_No : parcel.ParcelNo;
        propertyParcel.Lot = parcel.Lot;
        propertyParcel.ApplicationID = EnumApplication.VST;
        propertyParcel.EntityID = this._loginService.UserInfo.EntityID;
        propertyParcel.PropertyID = this.selectedPropertyID
        propertyParcel.Block = parcel.Block || null;
        propertyParcel.SubDivision = parcel.SubDivision || null;
        propertyParcel.ParcelSF = parcelSize ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', parcelSize): null;
        propertyParcel.ChangeLogJSON = null;
        const response_parcel = this._propertyService.PropertyParcelDetailsSave(propertyParcel);
        response_parcel.subscribe(result => {
          if (result.body && result.body.error) {
            this._notificationService.ShowErrorMessage(`Failed to add the parcel number ${parcel.Parcel_No} to property ${this.selectedPropertyID}`);
          }
          this.fromSelectParcels = false;
          this.onComplete();
        });
      });
      setTimeout(() => {
        let commModel = new CommunicationModel();
        commModel.Key = 'fetchParcels';
        commModel.data = { shouldUpdate: true };
        this._communicationService.broadcast(commModel);
      }, 1000);
    } 
  }

  onParcelsSave() {
    this.showMultiParcelConfirmationModal = false;
    this.saveParcelsInfo();
  }

  onParcelsCancel() {
    this.showMultiParcelConfirmationModal = false;
  }
}
