import { FormControl, FormGroup } from '@angular/forms';
import { environment } from '../../environments/environment';
import { ResearchType } from '../enumerations/researchType';
import { EnumRoofTopSource } from '../enumerations/roofTopSource';
import { EnumApplication } from '../enumerations/application';
import { EnumCondoTypeName } from '../enumerations/condoType';
import { EnumConstructionStatus } from '../enumerations/constructionStatus';
import { EnumSizeSource } from '../enumerations/sizeSource';
import { formatDateForDatePicker, getFormattedDate, getPreviousData, setPropertyName } from '../utils';
import { CheckboxFields } from '../enumerations/officeControlKeys';
import { UseTypes } from '../enumerations/useTypes';
import { dropdownFields, fieldUnitMap } from '../common/constants';
import { Property } from '../models/Property';
import { PropertyMasterRollup } from '../models/PropertyMasterRollup';
import { EditPropertyTabs } from '../enumerations/editPropertyTabs';
import { PropertyLocation } from '../models/PropertyLocation';

export function processPropertyResearchStatus(data: any[]): any[] {
  const researchTypeIcons = {
    [ResearchType.NeedsResearch]: environment.MapIconNeedsResearch,
    [ResearchType.BaseComplete]: environment.MapIconBaseResearchComplete,
    [ResearchType.FieldResearchComplete]: environment.MapIconFieldResearchComplete,
    [ResearchType.Hidden]: environment.MapIconHidden,
    [ResearchType.ExpressComplete]: environment.MapIconExpressComplete,
    [ResearchType.ExpressIncomplete]: environment.MapIconExpressIncomplete,
    [ResearchType.NotStarted]: environment.MapIconNotStarted
  };

  return (data || [])
    .sort((a, b) => Number(a.Sequence) - Number(b.Sequence))
    .map(status => ({
      ...status,
      IsActive: false,
      PropertyResearchStatusID: 0,
      ResearchStatusPin: researchTypeIcons[status.PropertyResearchTypeID] || environment.MapIconNotStarted
    }));
}

export function preparePropertyData(
  propertyCopy: any,
  property: any,
  isAnExistingProperty: boolean,
  hasNoExistingParcelInTileLayer: boolean,
  CountryId: number,
  _propertyService: any
): boolean {
  const convert = (from: string, to: string, value: number) =>
    _propertyService.convertUnit(CountryId, from, to, value);

  // Mapping and conversions
  propertyCopy.BuildingSizeSM = property.BuildingSF;
  propertyCopy.BuildingSizeSMFormatted = Math.round(property.BuildingSF);

  const sqmToSfFields = [
    'BuildingSF', 'LotSizeSF', 'TotalAnchorSF', 'OfficeSF', 'RetailSF',
    'SmallestFloor', 'LargestFloor', 'HardstandArea', 'TypicalFloorSizeSM',
    'BuildingNRA', 'ContributedGBA_SF', 'Mezzanine_Size_SF', 'Awnings_Size_SF',
    'GLAR_SF', 'GLA_SF', 'TotalLettableSize_SF', 'TypicalFloorSize',
  ];

  sqmToSfFields.forEach(field => {
    if (property[field] !== undefined) {
      propertyCopy[field] = convert('SqM', 'SF', property[field]);
    }
  });

  const ftToMFields = [
    'ColumnSpacingLen', 'ColumnSpacingWidth', 'BayWidth', 'BayDepth',
    'RetailFrontage', 'ClearHeightMin', 'ClearHeightMax', 'Depth', 'Width'
  ];

  ftToMFields.forEach(field => {
    if (property[field] !== undefined) {
      propertyCopy[field] = convert('ft', 'M', property[field]);
    }
  });

  propertyCopy.HasNoExistingParcelInTileLayer = isAnExistingProperty
    ? property.HasNoExistingParcelInTileLayer
    : hasNoExistingParcelInTileLayer ? 1 : 0;

  const dateFields = [
    'ConstructionStartDate', 'EstCompletionDate',
    'ActualCompletion', 'TitleReferenceDate', 'BookValueDate'
  ];

  dateFields.forEach(dateField => {
    propertyCopy[dateField] = propertyCopy[dateField]?.singleDate?.formatted ?? null;
  });

  if (propertyCopy.SpecificUseID === 0) {
    propertyCopy.SpecificUseID = null;
  }

  return false; // resets hasNoExistingParcelInTileLayer in caller
}

export function preparePropertyLocationData(
  propertyLocation: any,
  property: any,
  propertyCopy: any,
  initialDetails: any,
  isAnExistingProperty: boolean,
  isMultiStrata: boolean,
  selectedMasterParcel: string,
  CountryId: number,
  EntityID: number,
  DataArray: any[],
  propertyTypeValues: any,
  addressTypeValues: any,
  streetPrefixes: any[],
  streetSufixes: any[],
  quadrants: any[]
): void {
  const emptyStringFields = [
    'StreetPrefix1', 'StreetPrefix2', 'StreetSuffix1', 'StreetSuffix2', 'City', 'State', 'Quadrant',
    'RooftopSourceID', 'EastWestStreet', 'NorthSouthStreet', 'LegalDesc', 'Zip', 'Complex', 'PartOfComplex',
    'PartOfComplex'
  ];

  emptyStringFields.forEach(field => {
    if (propertyLocation[field] === '' || !propertyLocation[field]) {
      propertyLocation[field] = null;
    }
  });

  if (propertyLocation.County === 0) propertyLocation.County = null;
  if (property.MarketId === '') property.MarketId = null;
  if (property.SubMarketID === '') property.SubMarketID = null;
  if (property.ConstructionTypeID === '' || !property.ConstructionTypeID) property.ConstructionTypeID = null;

  if (!propertyLocation.RooftopSourceID && !isAnExistingProperty) {
    propertyLocation.RooftopSourceID = EnumRoofTopSource.Researcher;
  }
  if (propertyLocation.RooftopSourceID === '') {
    propertyLocation.RooftopSourceID = null;
  }

  ['PrimaryTrafficCountDate', 'SecTrafficCntDate'].forEach(dateField => {
    if (propertyLocation[dateField]) {
      propertyLocation[dateField] = propertyLocation[dateField]?.singleDate?.formatted;
    }
  });

  propertyLocation.EntityID = EntityID;
  propertyLocation.CountryID = CountryId;
  propertyLocation.ChangeLogJSON = JSON.stringify(DataArray);
  propertyLocation.ApplicationID = EnumApplication.VST;

  if (!isAnExistingProperty) {
    propertyLocation.StreetPrefix2 = propertyCopy.StreetPrefix2;

    if (
      (propertyLocation.Condo === EnumCondoTypeName.Strata ||
        propertyLocation.Condo === EnumCondoTypeName.Child_Freehold) &&
      !isMultiStrata
    ) {
      setPropertyName(
        propertyLocation,
        addressTypeValues,
        streetPrefixes,
        streetSufixes,
        quadrants
      );
      propertyLocation.PropertyName = `${propertyLocation.CondoUnit}/${propertyLocation.PropertyName}`;
    }

    if (
      propertyLocation.Condo === EnumCondoTypeName.Child_Freehold &&
      !isMultiStrata
    ) {
      const masterObj = initialDetails.masterStrataObj;
      if (masterObj) {
        property.ParcelNumber = masterObj.property.ParcelNumber;
        propertyCopy.ParcelNumber = masterObj.property.ParcelNumber;
      } else {
        if (property.ParcelNumber !== selectedMasterParcel) {
          property.ParcelNumber = selectedMasterParcel;
        }
        propertyCopy.ParcelNumber = property.ParcelNumber;
      }
    }

    if (!propertyLocation?.Condo && property?.UseTypeID !== propertyTypeValues.Land) {
      propertyLocation.Condo = EnumCondoTypeName.NotStrata;
    }

    if (!property?.ConstructionStatusID && property?.UseTypeID !== propertyTypeValues.Land) {
      property.ConstructionStatusID = EnumConstructionStatus.Existing;
    }

    if (!property?.LotSizeSourceID && (property?.LotSizeSM || property?.LotSizeSF)) {
      property.LotSizeSourceID = EnumSizeSource.CountyDataSource;
    }
  }

  if (initialDetails.selectedParcel?.ShapeID) {
    propertyLocation.GISShapeID = initialDetails.selectedParcel.ShapeID;
  } else if (initialDetails?.shapes?.length > 0) {
    propertyLocation.GISShapeID = initialDetails.shapes[0].ShapeID;
  }
}

export function mapChangeLogUtil(params: {
  form: any,
  isAnExistingProperty: boolean,
  property: any,
  propertyCopy: any,
  propertyLocation: any,
  propertyLocationCopy: any,
  oldPolygon: any,
  multifloors: any[],
  _loginService: any,
  _datePipe: any,
  dateFormat: string,
  propertyResearchStatus: any[],
  selectedOption: number,
  researchChanged: boolean,
  getValue: (key: string, val: any) => string,
  getDropdownFromLookup: (lookupKey: string) => any[],
  isAverageEstimationEnabled: boolean
  changeLogArray: any[]
}) {
  const {
    form,
    isAnExistingProperty,
    property,
    propertyCopy,
    propertyLocation,
    propertyLocationCopy,
    oldPolygon,
    multifloors,
    _loginService,
    _datePipe,
    dateFormat,
    propertyResearchStatus,
    selectedOption,
    researchChanged,
    getValue,
    getDropdownFromLookup,
    isAverageEstimationEnabled,
    changeLogArray
  } = params;

  const DataArray = [...changeLogArray];
  const date = new Date().toISOString();
  const data = form['controls'];
  delete data['TypicalFloorSizeSM'];

  function processControl(controlName: string, control: any) {
    if (control?.dirty === true) {
      const index = DataArray?.findIndex(x => x?.Field === controlName);
      if (index === -1) {
        if (Object.keys(dropdownFields).includes(controlName)) {
          const field = dropdownFields[controlName];
          DataArray.push({
            'Field': controlName,
            'CurrentValue': control?.value ? getPreviousData(controlName, property[field.propertyKeyName], getDropdownFromLookup(field.lookupKey)) : null,
            'PreviousValue': getPreviousData(controlName, propertyCopy[field.propertyKeyName], getDropdownFromLookup(field.lookupKey)),
            'LoginEntityID': _loginService.UserInfo.EntityID,
            'DateTime': date
          });
        } else {
          DataArray.push({
            Field: controlName === 'TypicalFloorSizeSM' ? 'TypicalFloorSize' : controlName,
            CurrentValue: CheckboxFields.includes(controlName)
              ? (control?.value ? 'Yes' : 'No')
              : getValue(controlName, control?.value),
            PreviousValue: CheckboxFields.includes(controlName)
              ? (propertyCopy[controlName] ? 'Yes' : 'No')
              : controlName === 'LegalDesc'
                ? propertyCopy['LegalDescription']
                : propertyCopy[controlName]
                  ? getValue(controlName, propertyCopy[controlName])
                  : getValue(controlName, propertyLocationCopy[controlName]),
            LoginEntityID: _loginService.UserInfo.EntityID,
            DateTime: date,
          });
        }
      }
    }
  }

  Object.keys(data).forEach((key) => {
    const control = data[key];

    if (control instanceof FormGroup) {
      Object.keys(control?.controls).forEach(nestedKey => {
        processControl(nestedKey, control?.controls[nestedKey]);
      });
    } else {
      processControl(key, control);
    }
  });

  if (isAnExistingProperty) {
    if (oldPolygon && !((propertyLocation.Condo === EnumCondoTypeName.Master && !isAverageEstimationEnabled) || propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || property.UseTypeID === UseTypes.Land)) {
      Object.keys(oldPolygon).forEach(footprint => {
        const polygon = multifloors.find(floor => floor.BuildingFootPrintID && floor.BuildingFootPrintID.toString() === footprint);
        if ((polygon && polygon.shape !== oldPolygon[footprint].footprint) || !polygon) {
          DataArray.push({
            Field: 'BuildingFootPrint',
            CurrentValue: {
              Floor: polygon ? `${polygon.minFloor}-${polygon.maxFloor}` : "N/A",
              footprint: polygon ? `POLYGON((${polygon.shape}))` : "",
            },
            PreviousValue: oldPolygon[footprint],
            LoginEntityID: _loginService.UserInfo.EntityID,
            DateTime: date
          });
        }
      });

      if (!DataArray.find(row => row.Field === 'BuildingSF')) {
        DataArray.push({
          Field: 'BuildingSF',
          CurrentValue: property.BuildingSF,
          PreviousValue: propertyCopy.BuildingSF,
          LoginEntityID: _loginService.UserInfo.EntityID,
          DateTime: date
        });
      }
    }

    if (multifloors.some(floor => floor.localBuildingFootPrintID?.length > 0)) {
      multifloors.forEach(floor => {
        if (floor.localBuildingFootPrintID) {
          DataArray.push({
            Field: 'BuildingFootPrint',
            CurrentValue: {
              Floor: `${floor.minFloor}-${floor.maxFloor}`,
              footprint: `POLYGON((${floor.shape}))`,
            },
            PreviousValue: {
              Floor: "N/A",
              footprint: "",
            },
            LoginEntityID: _loginService.UserInfo.EntityID,
            DateTime: date
          });
        }
      });
    }

    if (((propertyLocation.Condo === EnumCondoTypeName.Master && !isAverageEstimationEnabled) || propertyLocation.Condo === EnumCondoTypeName.Master_Freehold) && propertyLocation.Condo !== propertyLocationCopy.Condo && oldPolygon) {
      Object.keys(oldPolygon).forEach(footprint => {
        DataArray.push({
          Field: 'BuildingFootPrint',
          CurrentValue: {
            Floor: 'N/A',
            footprint: '',
          },
          PreviousValue: oldPolygon[footprint],
          LoginEntityID: _loginService.UserInfo.EntityID,
          DateTime: date
        });
      });
    }

    if (property?.IsReviewed) {
      DataArray.push({
        Field: 'LastReviewedBy',
        CurrentValue: _loginService?.UserInfo?.PersonName,
        PreviousValue: propertyCopy?.PropertyReviewedByName,
        LoginEntityID: _loginService?.UserInfo?.EntityID,
        DateTime: date
      });

      const currentReviewedDate = _datePipe.transform(new Date().toISOString(), dateFormat, '+0000') || null;
      let previousReviewedDate = null;

      if (propertyCopy?.LastReviewedDate && propertyCopy?.LastReviewedDate !== '00/00/0000') {
        previousReviewedDate = _datePipe.transform(propertyCopy?.LastReviewedDate, dateFormat, '+0000');
      }

      DataArray.push({
        Field: 'LastReviewedDate',
        CurrentValue: currentReviewedDate,
        PreviousValue: previousReviewedDate,
        LoginEntityID: _loginService.UserInfo.EntityID,
        DateTime: date
      });
    }

    if (researchChanged) {
      let currentData, previousData;
      propertyResearchStatus.forEach(element => {
        if (element.PropertyResearchTypeID === selectedOption) {
          currentData = element.PropertyResearchTypeName;
        }
        if (element.PropertyResearchTypeID === propertyCopy.PropertyResearchTypeID) {
          previousData = element.PropertyResearchTypeName;
        }
      });

      DataArray.push({
        Field: 'PropertyResearchType',
        CurrentValue: currentData,
        PreviousValue: previousData,
        LoginEntityID: _loginService.UserInfo.EntityID,
        DateTime: date
      });
    }

    if (propertyCopy.HasNoBuildingFootprints != property.HasNoBuildingFootprints) {
      DataArray.push({
        Field: 'HasNoBuildingFootprints',
        CurrentValue: property.HasNoBuildingFootprints ? 'Yes' : 'No',
        PreviousValue: propertyCopy.HasNoBuildingFootprints ? 'Yes' : 'No',
        LoginEntityID: _loginService.UserInfo.EntityID,
        DateTime: date
      });
    }
  }

  return DataArray;
}

export function getCondoType(CondoTypeID: number): string {
  switch (CondoTypeID) {
    case 1:
      return 'Not Strata';
    case 2:
      return 'Strata';
    case 3:
      return 'Master Strata';
    case 4:
      return 'Master Freehold';
    case 5:
      return 'Freehold'
  }
}

export function preparePropertyFromResult({
  result,
  component,
  form,
  unitId,
  dateFormat,
  datePipe,
  sharedDataService
}: {
  result: any,
  component: any,
  form: FormGroup,
  unitId: number,
  dateFormat: string,
  datePipe: any,
  sharedDataService: any
}) {
  if (!result.body.error) {
    if (component.redirectionLoader) {
      component.redirectionLoader = false;
    }

    const property = result.body.Property.responseData[0];
    const rollup = result.body.Property.responseData[1];

    component.property = new Property();
    component.propertyCopy = new Property();
    component.rollupMasterFreeholdFieldsObject = new PropertyMasterRollup();

    component.property = property;
    component.footPrintNotAvailable = property.HasNoBuildingFootprints === 1;

    console.log('API Property ContributedGBA_SF:', property.ContributedGBA_SF);
    console.log('API Property HasNoBuildingFootprints:', property.HasNoBuildingFootprints);
    console.log('footPrintNotAvailable set to:', component.footPrintNotAvailable);

    if (component.footPrintNotAvailable) {
      form.addControl('ContributedGBA_SF', new FormControl(property.ContributedGBA_SF));
      form.addControl('ContributedGBASource', new FormControl(property.ContributedGBASizeSourceID));
    }

    component.rollupMasterFreeholdFieldsObject = rollup;
    component.IsSkipped = property.IsSkipped;

    const isMetric = component.metricUnit === unitId;
    Object.keys(fieldUnitMap).forEach((key) => {
      const sourceField = fieldUnitMap[key][isMetric ? 'meter' : 'feet'];
      component.property[key] = property[sourceField];
    
      if (rollup) {
        component.rollupMasterFreeholdFieldsObject[key] = rollup[sourceField];
      }
      component.property.TotalLettableSize_SF = Number(component.property.TotalLettableSize_SF);
      component.property.TypicalFloorSize = Number(component.property.TypicalFloorSize)
    });
    

    Object.assign(component.property, {
      SizeSourceID: property.BldgSizeSourceID,
      Anchors: property.NoOfAnchor,
      YardPaved: property.HasPavedYard,
      NoOfOfficeFloor: property.NoOfOfficeFloors,
      HasReservedCoveredParking: property.HasResCoveredParking,
      HasReservedSurfaceParking: property.HasResSurfaceParking,
      BuildingClass: property.ClassTypeID
    });

    Object.assign(component.propertyLocation, {
      PropertyID: property.PropertyID,
      PropertyName: property.PropertyName
    });

    component.previousPropertyName = property.PropertyName;
    sharedDataService.selectedFloor = property.Floors;
    component.property.MarketId = property.MarketId || undefined;
    component.property.SubMarketID = property.SubMarketID || undefined;
    component.property.ConstructionTypeID = property.ConstructionTypeID || undefined;

    if (rollup) {
      const dateFields = ['ConstructionStartDate', 'EstCompletionDate', 'ActualCompletion', 'TitleReferenceDate', 'BookValueDate'];
      dateFields.forEach(field => {
        const val = rollup[field] ?? '';
        const parts = val.split(' - ');
        if (parts.length === 2) {
          component.rollupMasterFreeholdFieldsObject[field] =
            `${getFormattedDate(parts[0], dateFormat)} - ${getFormattedDate(parts[1], dateFormat)}`;
        }
      });
    }

    const singleDateFields = [
      'ConstructionStartDate',
      'EstCompletionDate',
      'LastRenovationDate',
      'BookValueDate',
      'ActualCompletion',
      'TitleReferenceDate'
    ];
    singleDateFields.forEach(field => {
      const value = component.property[field];
      if (value && value !== '00/00/0000') {
        const formatted = datePipe?.transform(value, dateFormat, '+0000');
        component.property[field] = formatDateForDatePicker(value);
        component.property[field].singleDate.formatted = formatted;
      }
    });

    component.updateAdditionalUsesList();
    component.propertyCopy = JSON.parse(JSON.stringify(component.property));
    component.location = {
      Latitude: component.property.Latitude,
      Longitude: component.property.Longitude
    };

    form.get('IsAverageEstimationEnabled').setValue(!!component.property.IsAverageEstimationEnabled);
    component.isAverageEstimationEnabled = !!component.property.IsAverageEstimationEnabled;

    if (component.isAnExistingProperty) {
      component.selectedFloor = property.Floors;
      sharedDataService.selectedFloor = property.Floors;
      component.selectedUseTypeID = property.UseTypeID;
      const previousUseType = property.UseTypeID;
      component.onPropertyUseChange(false, property.UseTypeID || 5, previousUseType);
      component.resetControls();
      if (component.propertyParcelList?.length) {
        component.property.ParcelNumber = component.propertyParcelList[0].ParcelNo;
      }
    } else {
      const floors = JSON.stringify(JSON.parse(property.Floors));
      const useType = JSON.stringify(JSON.parse(property.UseTypeID));
      component.property.Floors = floors;
      component.property.UseTypeID = useType;
      component.selectedFloor = floors;
      component.selectedUseTypeID = useType;
      sharedDataService.selectedFloor = floors;
    }

    component.setFloorOptions();
    component.showMapModal(false, null, null, false);
    form.get('PropertyTypeName')?.setValue(component.property.UseTypeID);
  }
}

export function preparePropertyLocationFromResult({
  result,
  newPropertyLocation,
  isAnExistingProperty,
  _datePipe,
  dateFormat,
  buildAddress,
  onPropertyFetched,
  setTab
}: {
  result: any;
  newPropertyLocation: any;
  isAnExistingProperty: boolean;
  _datePipe: any;
  dateFormat: string;
  buildAddress: () => void;
  onPropertyFetched: () => void;
  setTab: (tab: EditPropertyTabs) => void;
}): PropertyLocation {
  const address = result.body.responseData[0];
  let location = new PropertyLocation();

  Object.assign(location, address);
  location.AddressType = address.IsIntersection || 0;
  location.StreetPrefix1 = address.PrefixID || undefined;
  location.StreetPrefix2 = address.Prefix2ID || undefined;
  location.State = address.StateID || undefined;
  location.City = address.CityID || undefined;
  location.StreetSuffix1 = address.SuffixID || undefined;
  location.StreetSuffix2 = address.Suffix2ID || undefined
  location.Quadrant = address.QuadrantID || undefined;
  location.Latitude = newPropertyLocation?.Latitude || address.Latitude;
  location.Longitude = newPropertyLocation?.Longitude || address.Longitude;
  location.County = address.CountyID || undefined;
  location.EastWestStreet = address.EastWestSt || undefined;
  location.NorthSouthStreet = address.NorthSouthSt || undefined;
  location.LegalDesc = address.LegalDescription || undefined;
  location.Zip = address.ZipCode || undefined;
  location.Complex = address.ComplexName || undefined;
  location.Condo = address.CondoTypeID || undefined;
  location.MasterPropertyId = address.MasterPropertyID;

  if (isAnExistingProperty) {
    const showStrata = address.CondoTypeID === EnumCondoTypeName.Strata || address.CondoTypeID === EnumCondoTypeName.Master;
    const showFreehold = address.CondoTypeID === EnumCondoTypeName.Child_Freehold || address.CondoTypeID === EnumCondoTypeName.Master_Freehold;
    if (showStrata || showFreehold) {
      setTab(EditPropertyTabs.Strata);
    }
  }

  location.PartOfComplex = address.PartOfCenterComplex || undefined;
  location.RooftopSourceID = address.RooftopSourceID || undefined;
  location.PrimaryTrafficCountDate = address.PrimaryTrafficCountDate;
  location.SecondaryTrafficCountDate = address.SecondaryTrafficCountDate;

  if (location?.PrimaryTrafficCountDate && location.PrimaryTrafficCountDate !== '00/00/0000') {
    const formatted = _datePipe?.transform(location.PrimaryTrafficCountDate, dateFormat, '+0000');
    location.PrimaryTrafficCountDate = formatDateForDatePicker(location.PrimaryTrafficCountDate);
    location.PrimaryTrafficCountDate.singleDate.formatted = formatted;
  }

  if (location?.SecondaryTrafficCountDate && location.SecondaryTrafficCountDate !== '00/00/0000') {
    const formatted = _datePipe?.transform(location.SecondaryTrafficCountDate, dateFormat, '+0000');
    location.SecTrafficCntDate = formatDateForDatePicker(location.SecondaryTrafficCountDate);
    location.SecTrafficCntDate.singleDate.formatted = formatted;
  }

  buildAddress();
  onPropertyFetched();

  return location;
}

export interface BuildAddressParams {
  propertyLocation: any;
  streetSuffixes: { SuffixId: any; Suffix: string }[];
  cities: { CityID: any; CityName: string }[];
  states: { StateID: any; StateAbbr: string }[];
}

export function buildAddressDetails({
  propertyLocation,
  streetSuffixes,
  cities,
  states
}: BuildAddressParams): {
  StreetNumber: string;
  StreetSuffix1Text?: string;
  CityName?: string;
  StateName?: string;
} {
  let StreetNumber = '';
  if (propertyLocation.StreetNumberMin && !propertyLocation.StreetNumberMax) {
    StreetNumber = propertyLocation.StreetNumberMin;
  } else if (!propertyLocation.StreetNumberMin && propertyLocation.StreetNumberMax) {
    StreetNumber = propertyLocation.StreetNumberMax;
  } else if (
    propertyLocation.StreetNumberMin &&
    propertyLocation.StreetNumberMax &&
    propertyLocation.StreetNumberMin === propertyLocation.StreetNumberMax
  ) {
    StreetNumber = propertyLocation.StreetNumberMin;
  } else if (
    propertyLocation.StreetNumberMin &&
    propertyLocation.StreetNumberMax &&
    propertyLocation.StreetNumberMin !== propertyLocation.StreetNumberMax
  ) {
    StreetNumber = `${propertyLocation.StreetNumberMin}-${propertyLocation.StreetNumberMax}`;
  }

  const StreetSuffix1Text = propertyLocation.StreetSuffix1
    ? streetSuffixes.find(item => item.SuffixId === propertyLocation.StreetSuffix1)?.Suffix
    : undefined;

  const CityName = propertyLocation.City
    ? cities.find(item => item.CityID === propertyLocation.City)?.CityName
    : undefined;

  const StateName = propertyLocation.State
    ? states.find(item => item.StateID === propertyLocation.State)?.StateAbbr
    : undefined;

  return {
    StreetNumber,
    StreetSuffix1Text,
    CityName,
    StateName
  };
}

